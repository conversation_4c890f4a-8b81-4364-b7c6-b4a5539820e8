import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { knex } from 'knex';
import * as config from '../../knexfile';
import type { Knex } from 'knex';

export const KnexProvider = {
  provide: 'KNEX_CONNECTION',
  inject: [ConfigService],
  useFactory: (configService: ConfigService) => {
    const environment = configService.getOrThrow<string>('NODE_ENV');
    const knexConfig = (config as { [key: string]: Knex.Config })[environment];
    return knex(knexConfig);
  },
};

@Module({
  imports: [ConfigModule],
  providers: [KnexProvider],
  exports: [KnexProvider],
})
export class DatabaseModule {}
