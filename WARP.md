# WARP.md

This file provides guidance to WARP (warp.dev) when working with code in this repository.

## Development Commands

### Setup and Installation
```bash
npm install
```

### Running the Application
```bash
# Development mode with hot reload
npm run start:dev

# Standard development mode
npm run start

# Debug mode
npm run start:debug

# Production mode (includes DB migration)
npm run start:prod
```

### Database Management
```bash
# Run latest migrations
npm run migrate:latest

# Create new migration
npm run migrate:make <migration_name>

# Rollback migrations
npm run migrate:rollback

# Create seed file
npm run seed:make <seed_name>

# Run seeds
npm run seed:run

# Production migrations (uses dist folder)
npm run migrate:latest:prod
npm run migrate:rollback:prod
npm run seed:run:prod
```

### Testing
```bash
# Unit tests
npm run test

# Watch mode for tests
npm run test:watch

# Test coverage
npm run test:cov

# End-to-end tests
npm run test:e2e

# Debug tests
npm run test:debug
```

### Code Quality
```bash
# ESLint with auto-fix
npm run lint

# Prettier formatting
npm run format

# Build the application
npm run build
```

### Docker Development
```bash
# Start all services (requires .env.docker.local file)
docker compose up -d

# Run migrations inside app container
docker compose exec app npm run migrate:latest
```

## High-Level Architecture

This is a NestJS-based datalake API that processes and manages website content, articles, and videos with AI-powered analysis capabilities.

### Core Domain Structure

**Websites Module**: Central entity that manages website URLs and configurations
- Automatically discovers sitemaps from website URLs
- Controls scraping behavior and ad-check frequency
- Orchestrates article discovery and processing

**Articles Module**: Processes individual articles from websites
- Extracts content and generates AI-powered keywords using OpenAI GPT-4
- Analyzes thumbnails for additional food-related keywords (German)
- Manages embeddings for semantic search capabilities
- Stores ad-check results from Lambda function analysis

**Videos Module**: Manages video content with keyword extraction
- Handles video metadata and creator associations
- Integrates with queue system for embedding generation

**Sitemaps Module**: Manages sitemap discovery and parsing
- Links websites to their discovered sitemaps
- Enables structured content discovery

### Infrastructure Components

**Database Layer**: PostgreSQL with Knex.js query builder
- Environment-aware configuration (development/production)
- SSL support for production with certificate handling
- Migration and seeding system

**Queue System**: Redis-backed BullMQ for async processing
- `scrape` queue: Content extraction from article URLs  
- `embedding` queue: AI embedding generation for semantic search
- `ad-check` queue: Advertisement compliance checking

**AWS Integration**: Lambda functions for specialized processing
- Ad-checking service integration via AWS Lambda invocation
- Configurable Lambda URL through environment variables

**Scheduled Tasks**: Cron-based automated processes
- Daily sitemap updates at midnight
- Website content freshness checks

### Data Flow Architecture

1. **Website Registration**: URL → Sitemap Discovery → Article URL Extraction → Queue Jobs
2. **Content Processing**: Scrape Queue → Content Extraction → Keyword Generation → Embedding Creation
3. **Ad Compliance**: Manual/Scheduled Triggers → Lambda Processing → Result Storage
4. **Search Capabilities**: Embedding-based semantic search across articles and videos

### Key Integration Patterns

- **OpenAI Integration**: GPT-4 for text keyword extraction and vision for thumbnail analysis
- **Modular Architecture**: Each domain (websites, articles, videos, sitemaps) is encapsulated in its own NestJS module
- **Queue-Driven Processing**: Heavy operations (scraping, embeddings, ad-checks) are processed asynchronously
- **Environment Configuration**: Joi-validated environment variables with development/production configurations

### Development Guidelines

Follow the Cursor rules defined in `.cursor/rules/cursor-rule.mdc`:
- Use TypeScript with explicit typing (avoid `any`)
- Follow NestJS modular architecture patterns
- Prefer composition over inheritance
- Write functions with single purpose (< 20 instructions)
- Use SOLID principles for class design
- Follow Arrange-Act-Assert for tests
- Use JSDoc for public methods documentation
- Prefer immutability and readonly for data that doesn't change

### Environment Variables Required

- `DATABASE_HOST`, `DATABASE_PORT`, `DATABASE_USER`, `DATABASE_PASSWORD`, `DATABASE_NAME`
- `REDIS_URL` for queue management
- `OPENAI_API_KEY` for AI processing
- `AD_CHECK_LAMBDA_URL` for ad compliance checking
- `NODE_ENV` (production/development)