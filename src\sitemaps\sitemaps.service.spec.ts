import { Test, TestingModule } from '@nestjs/testing';
import { SitemapsService } from './sitemaps.service';

describe('SitemapsService', () => {
  let service: SitemapsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SitemapsService,
        {
          provide: 'KNEX_CONNECTION',
          useValue: {
            select: jest.fn(),
            where: jest.fn(),
            insert: jest.fn(),
            update: jest.fn(),
            del: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<SitemapsService>(SitemapsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
