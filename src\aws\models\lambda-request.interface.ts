import { AdCheckJobResult } from 'src/ad-checker/models/ad-check-job-result.interface';

export interface LambdaRequest {
  metadata?: Record<string, unknown>;
}

export type LambdaResponse<T> = LambdaErrorResponse | LambdaSuccessResponse<T>;

export interface LambdaErrorResponse {
  success: false;
  error: string;
}

export interface LambdaSuccessResponse<T> {
  success: true;
  result: T;
}

export interface AdCheckRequest extends LambdaRequest {
  url: string;
  articleId: number;
  layers: ['placement', 'visibility'];
}

export type AdCheckResponse = LambdaResponse<AdCheckJobResult>;
