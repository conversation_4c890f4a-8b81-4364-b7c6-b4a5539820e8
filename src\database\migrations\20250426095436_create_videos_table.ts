import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('videos', (table) => {
      table.string('id').primary();
      table.string('creator_id').notNullable();
      table.specificType('keywords', 'text[]').notNullable();
      table.specificType('embedding', 'vector(768)');
      table.timestamps(true, true);
    })
    .raw(
      'CREATE INDEX videos_embedding_idx ON videos USING ivfflat (embedding vector_l2_ops)',
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('videos');
}
