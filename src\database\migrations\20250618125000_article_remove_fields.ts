import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('articles', (table) => {
    table.dropColumn('embedding2');
    table.dropColumn('thumbnail_url');
    table.dropColumn('thumbnail_keywords');
    table.dropColumn('thumbnail_embedding');
    table.dropColumn('combined_keywords');
    table.dropColumn('combined_embedding');
    table.dropColumn('openai_embeddings');
    table.dropColumn('openai_embeddings_small');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('articles', (table) => {
    table.specificType('embedding2', 'vector(768)');
    table.string('thumbnail_url');
    table.specificType('thumbnail_keywords', 'text[]');
    table.specificType('thumbnail_embedding', 'vector(768)');
    table.specificType('combined_keywords', 'text[]');
    table.specificType('combined_keywords_embedding', 'vector(768)');
    table.specificType('openai_embeddings', 'vector(3072)');
    table.specificType('openai_embeddings_small', 'vector(768)');
  });
}
