services:
  db:
    build:
      context: .
      dockerfile: ./docker/Dockerfile.db
    restart: always
    environment:
      - POSTGRES_PASSWORD=postgres
    container_name: postgres
    volumes:
      - ./docker/pgdata:/var/lib/postgresql/data
      - ./docker/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - '5432:5432'

  redis:
    image: redis:latest
    healthcheck:
      test: ['CMD-SHELL', 'redis-cli ping | grep PONG']
      interval: 1s
      timeout: 3s
      retries: 5
    command: ['redis-server']
    container_name: redis
    ports:
      - '6379:6379'

  app:
    depends_on:
      redis:
        condition: service_healthy
    build:
      context: .
      dockerfile: ./Dockerfile
    env_file:
      - .env.docker.local
    ports:
      - '3000:3000'
    volumes:
      - .:/usr/src/app
    command: npm run start:dev
    container_name: datalake-app

  # ad_checker:
  #   depends_on:
  #     redis:
  #       condition: service_healthy
  #   build:
  #     context: ../ad-checker/
  #     dockerfile: Dockerfile
  #   environment:
  #     REDIS_HOST: redis
  #     REDIS_PORT: 6379
  #   container_name: ad-checker


