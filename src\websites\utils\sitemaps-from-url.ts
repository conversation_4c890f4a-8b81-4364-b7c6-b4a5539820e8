export async function sitemapsFromUrl(url: string) {
  const sitemapUrls: string[] = [];

  try {
    // Try robots.txt first
    const robotsUrl = new URL('/robots.txt', url).toString();
    const robotsResponse = await fetch(robotsUrl);

    if (robotsResponse.ok) {
      const robotsText = await robotsResponse.text();
      const sitemapMatches = robotsText.matchAll(/Sitemap:\s*(.+)/gi);

      for (const match of sitemapMatches) {
        sitemapUrls.push(match[1].trim());
      }
    }

    // If no sitemaps found in robots.txt, try common locations
    if (sitemapUrls.length === 0) {
      const commonSitemapPaths = [
        '/sitemap.xml',
        '/sitemap_index.xml',
        '/sitemap/sitemap.xml',
      ];

      for (const path of commonSitemapPaths) {
        const sitemapUrl = new URL(path, url).toString();
        const response = await fetch(sitemapUrl, { method: 'HEAD' });

        if (response.ok) {
          sitemapUrls.push(sitemapUrl);
          break;
        }
      }
    }

    // Check if any found sitemap is a sitemap index
    for (const sitemapUrl of [...sitemapUrls]) {
      try {
        const response = await fetch(sitemapUrl);
        const text = await response.text();

        // Check if it's a sitemap index by looking for <sitemapindex> tag
        if (text.includes('<sitemapindex')) {
          const matches = text.matchAll(/<loc>(.+?)<\/loc>/g);
          for (const match of matches) {
            const containedSitemapUrl = match[1].trim();
            if (!sitemapUrls.includes(containedSitemapUrl)) {
              sitemapUrls.push(containedSitemapUrl);
            }
          }
          // Remove the sitemap index URL since we've extracted its contents
          const indexToRemove = sitemapUrls.indexOf(sitemapUrl);
          if (indexToRemove !== -1) {
            sitemapUrls.splice(indexToRemove, 1);
          }
        }
      } catch (error) {
        console.warn(`Failed to check sitemap index ${sitemapUrl}:`, error);
      }
    }
  } catch (error) {
    console.warn(`Failed to fetch sitemap for ${url}:`, error);
  }

  return sitemapUrls;
}
