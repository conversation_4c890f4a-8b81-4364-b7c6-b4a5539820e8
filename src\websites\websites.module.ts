import { Module } from '@nestjs/common';
import { DatabaseModule } from '../database/database.module';
import { WebsitesService } from './websites.service';
import { WebsitesController } from './websites.controller';
import { ArticlesModule } from 'src/articles/articles.module';
import { QueueModule } from 'src/queue/queue.module';
import { SitemapsModule } from 'src/sitemaps/sitemaps.module';
import { AwsModule } from 'src/aws/aws.module';

@Module({
  imports: [
    DatabaseModule,
    ArticlesModule,
    QueueModule,
    SitemapsModule,
    AwsModule,
  ],
  controllers: [WebsitesController],
  providers: [WebsitesService],
  exports: [WebsitesService],
})
export class WebsitesModule {}
