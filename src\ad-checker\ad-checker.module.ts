import { Module } from '@nestjs/common';
import { AdCheckerService } from './ad-checker.service';
import { WebsitesModule } from 'src/websites/websites.module';
import { DatabaseModule } from 'src/database/database.module';
import { QueueModule } from 'src/queue/queue.module';
import { ArticlesModule } from 'src/articles/articles.module';
import { AdCheckerController } from './ad-checker.controller';

@Module({
  imports: [DatabaseModule, ArticlesModule, QueueModule, WebsitesModule],
  controllers: [AdCheckerController],
  providers: [AdCheckerService],
})
export class AdCheckerModule {}
