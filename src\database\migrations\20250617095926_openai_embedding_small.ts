import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('articles', (table) => {
    table.specificType('openai_embeddings_small', 'vector(1536)');
  });

  await knex.schema.alterTable('videos', (table) => {
    table.specificType('openai_embeddings_small', 'vector(1536)');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('articles', (table) => {
    table.dropColumn('openai_embeddings_small');
  });
  await knex.schema.alterTable('videos', (table) => {
    table.dropColumn('openai_embeddings_small');
  });
}
