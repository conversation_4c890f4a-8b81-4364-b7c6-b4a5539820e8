import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { WebsitesService } from './websites.service';
import { AdCheckFrequency } from './models/ad-check-frequency.enum';

@Controller('websites')
export class WebsitesController {
  constructor(private readonly websitesService: WebsitesService) {}

  @Post('create')
  async create(
    @Body()
    body: {
      url: string;
      creatorId: string;
      adCheckFrequency?: AdCheckFrequency;
      scrappingEnabled?: boolean;
    },
  ) {
    return this.websitesService.createWebsite(
      body.url,
      body.creatorId,
      body.adCheckFrequency || AdCheckFrequency.never,
      body.scrappingEnabled || false,
    );
  }

  @Get('ad-check-results')
  getAdCheckResults(@Query('url') url: string) {
    if (!url) {
      throw new HttpException(
        'URL parameter is required',
        HttpStatus.BAD_REQUEST,
      );
    }
    return this.websitesService.getAdCheckResults(url);
  }

  @Get('request-ad-check')
  requestAdCheck(@Query('url') url: string) {
    if (!url) {
      throw new HttpException(
        'URL parameter is required',
        HttpStatus.BAD_REQUEST,
      );
    }
    return this.websitesService.requestAdCheck(url);
  }

  @Post('check-sitemaps-update')
  async checkSitemapsUpdate() {
    return this.websitesService.initiateSitemapsUpdateCheck();
  }
}
