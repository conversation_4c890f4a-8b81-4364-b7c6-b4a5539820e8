import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('videos', (table) => {
    table.specificType('combined_keywords', 'text[]').nullable();
    table.specificType('combined_embedding', 'vector(768)').nullable();
  });

  await knex.schema.alterTable('articles', (table) => {
    table.specificType('combined_keywords', 'text[]').nullable();
    table.specificType('combined_embedding', 'vector(768)').nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('videos', (table) => {
    table.dropColumn('combined_keywords');
    table.dropColumn('combined_embedding');
  });

  await knex.schema.alterTable('articles', (table) => {
    table.dropColumn('combined_keywords');
    table.dropColumn('combined_embedding');
  });
}
