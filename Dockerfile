FROM node:22.17-alpine AS base
WORKDIR /usr/src/app


# Stage 1: Dependencies (with dev)
FROM base AS deps
COPY package*.json ./
RUN npm ci --include=dev


# Stage 2: Build
FROM base AS builder
COPY --from=deps /usr/src/app/node_modules ./node_modules
COPY . .
RUN npm run build


# Stage 3: Prod Dependencies (without dev)
FROM base AS prod-deps
COPY package*.json ./
RUN npm ci --omit=dev


# Stage 4: Runner
FROM base AS runner
COPY --from=prod-deps /usr/src/app/node_modules ./node_modules
COPY --from=builder /usr/src/app/dist ./dist
COPY package*.json ./



EXPOSE 3000
CMD ["node", "/usr/src/app/dist/src/main.js"]
