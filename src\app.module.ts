import { Modu<PERSON> } from '@nestjs/common';
import { VideosModule } from './videos/videos.module';
import { WebsitesModule } from './websites/websites.module';
import { ArticlesModule } from './articles/articles.module';
import { SitemapsModule } from './sitemaps/sitemaps.module';
import { ConfigModule } from '@nestjs/config';
import * as Joi from 'joi';
import { ScheduleModule } from '@nestjs/schedule';
import { AdCheckerModule } from './ad-checker/ad-checker.module';
import { AwsModule } from './aws/aws.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      validationSchema: Joi.object({
        DATABASE_HOST: Joi.string().required(),
        DATABASE_PORT: Joi.number().required(),
        DATABASE_USER: Joi.string().required(),
        DATABASE_PASSWORD: Joi.string().required(),
        DATABASE_NAME: Joi.string().required(),
        REDIS_URL: Joi.string().required(),
        OPENAI_API_KEY: Joi.string().required(),
        NODE_ENV: Joi.string().valid('production', 'development').required(),
        AD_CHECK_LAMBDA_URL: Joi.string().optional(),
      }),
    }),
    VideosModule,
    WebsitesModule,
    ArticlesModule,
    SitemapsModule,
    AdCheckerModule,
    AwsModule,
    ScheduleModule.forRoot(),
  ],
})
export class AppModule {}
