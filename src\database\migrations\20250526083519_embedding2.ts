import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('articles', (table) => {
    table.specificType('embedding2', 'vector(768)');
  });
  await knex.schema.alterTable('videos', (table) => {
    table.specificType('embedding2', 'vector(768)');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('articles', (table) => {
    table.dropColumn('embedding2');
  });
  await knex.schema.alterTable('videos', (table) => {
    table.dropColumn('embedding2');
  });
}
