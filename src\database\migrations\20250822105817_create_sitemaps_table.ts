import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('sitemaps', (table) => {
    table.increments('id').primary().unique();
    table.string('url').notNullable();
    table.integer('website_id').notNullable();
    table.timestamps(true, true);

    table.foreign('website_id').references('websites.id').onDelete('CASCADE');

    table.index(['website_id']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('sitemaps');
}
