import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Knex } from 'knex';
import { OpenAI } from 'openai';
import { AdCheckJobResult } from 'src/ad-checker/models/ad-check-job-result.interface';

export interface Article {
  id: number;
  url: string;
  website_id: number;
  textContent: string;
  keywords: string[];
  embedding: number[];
  ad_check_result?: AdCheckJobResult;
  created_at: Date;
  updated_at: Date;
}

@Injectable()
export class ArticlesService {
  private openai: OpenAI;

  constructor(
    @Inject('KNEX_CONNECTION') private knex: Knex,
    private configService: ConfigService,
  ) {
    this.openai = new OpenAI({
      apiKey: this.configService.getOrThrow<string>('OPENAI_API_KEY'),
    });
  }

  async getArticles() {
    return this.knex<Article>('articles').select('*');
  }

  async createArticle(url: string, websiteId: number, sitemapId: number) {
    const [article] = await this.knex<Article>('articles')
      .insert({
        url,
        website_id: websiteId,
      })
      .returning('*');

    await this.knex('sitemap_articles').insert({
      article_id: article.id,
      sitemap_id: sitemapId,
    });

    return article;
  }

  async updateArticle(url: string, textContent: string) {
    try {
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content:
              'Extract up to 10 relevant keywords from the following text, return only the keywords separated by commas.',
          },
          {
            role: 'user',
            content: textContent,
          },
        ],
      });

      const textKeywords = completion?.choices[0].message.content
        ?.split(',')
        .map((k) => k.trim());

      // Fetch the page and extract og:image
      const response = await fetch(url);
      const html = await response.text();
      const thumbnailUrl =
        html.match(
          /<meta[^>]*property="og:image"[^>]*content="([^"]*)"[^>]*>/,
        )?.[1] ||
        html.match(
          /<meta[^>]*name="twitter:image"[^>]*content="([^"]*)"[^>]*>/,
        )?.[1] ||
        '';

      let thumbnailKeywords: string[] = [];

      try {
        if (!thumbnailUrl) {
          throw new Error('No thumbnail url found');
        }

        const aiResponse = await this.openai.chat.completions.create({
          model: 'gpt-4o-mini',
          messages: [
            {
              role: 'system',
              content: `
              # Identity
              You are a picture analyzer that evaluates keywords from given video thumbnails.

              # Instructions
              * Analyze the picture and find the food that is visible in the picture
              * Generate german keywords for the food that is visible in the picture, nouns only

              # Output
              Your output is an array don't surround it with \`\`\`json\`\`\`. The strings should be surrounded by double quotes.
            `,
            },
            {
              role: 'user',
              content: [
                {
                  type: 'image_url',
                  image_url: {
                    url: thumbnailUrl,
                    detail: 'low',
                  },
                },
              ],
            },
          ],
          temperature: 0,
          max_tokens: 1000,
        });

        thumbnailKeywords = JSON.parse(
          aiResponse.choices[0]?.message?.content
            ?.replace(/```json/g, '')
            .replace(/```/g, '') || '[]',
        ) as string[];
      } catch (error) {
        console.log('Error getting thumbnail keywords', error);
        thumbnailKeywords = [];
      }

      if (!textKeywords) {
        return null;
      }

      // Combine text keywords with any existing keywords
      const keywords = [...textKeywords, ...thumbnailKeywords];

      // Update both textContent and keywords
      await this.knex<Article>('articles').where('url', url).update({
        textContent,
        keywords,
      });

      return keywords;
    } catch (error) {
      console.error(error);
    }
  }

  async addEmbedding(url: string, embedding: string) {
    return this.knex<Article>('articles')
      .where('url', url)
      .update({
        embedding: this.knex.raw('?', [embedding]),
      });
  }

  async updateArticleById(
    id: number | string,
    data: Partial<Article>,
  ): Promise<Article | undefined> {
    const [updatedArticle] = await this.knex<Article>('articles')
      .where('id', id)
      .update(data)
      .returning('*');
    return updatedArticle;
  }
}
