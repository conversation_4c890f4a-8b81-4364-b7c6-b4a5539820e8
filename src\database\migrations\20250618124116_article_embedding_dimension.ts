import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('articles', (table) => {
    table.dropColumn('embedding');
  });

  await knex.schema.alterTable('articles', (table) => {
    table.specificType('embedding', 'vector(1536)');
  });

  await knex.raw(
    'CREATE INDEX articles_embedding_idx ON articles USING ivfflat (embedding vector_l2_ops)',
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.raw('DROP INDEX articles_embedding_idx');

  await knex.schema.alterTable('articles', (table) => {
    table.dropColumn('embedding');
  });

  await knex.schema.alterTable('articles', (table) => {
    table.specificType('embedding', 'vector(768)');
  });

  await knex.raw(
    'CREATE INDEX articles_embedding_idx ON articles USING ivfflat (embedding vector_l2_ops)',
  );
}
