import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('recommendations', function (table) {
    table.increments('id').primary();
    table
      .integer('source_article_id')
      .references('id')
      .inTable('recommendation_articles')
      .onDelete('CASCADE');
    table
      .integer('recommended_article_id')
      .references('id')
      .inTable('recommendation_articles')
      .onDelete('CASCADE');
    table.float('score').notNullable();
    table.timestamps(true, true);

    table.index('source_article_id');
    table.index('recommended_article_id');
    table.index('score');

    table.unique(['source_article_id', 'recommended_article_id']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('recommendations');
}
