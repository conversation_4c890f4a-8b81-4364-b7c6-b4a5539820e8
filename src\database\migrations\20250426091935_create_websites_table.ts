import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('websites', (table) => {
    table.increments('id').primary();
    table.string('url').notNullable();
    table.specificType('sitemap_urls', 'text[]').notNullable();
    table.string('creator_id').notNullable();
    table.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('websites');
}
