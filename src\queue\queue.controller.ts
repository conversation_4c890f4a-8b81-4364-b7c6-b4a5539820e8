import { Controller, Post, Body, Get } from '@nestjs/common';
import { QueueService } from './queue.service';

@Controller('queue')
export class QueueController {
  constructor(private readonly queueService: QueueService) {}

  @Get('ad-checker-jobs-count')
  async getAdCheckerJobsCount() {
    return this.queueService.getAdCheckerJobsCount();
  }

  @Post('scrape')
  async scrape(@Body() body: { url: string }) {
    return this.queueService.addScrapeJob(body.url);
  }

  @Post('embed-articles')
  async embedArticles() {
    return this.queueService.embedArticles();
  }
}
