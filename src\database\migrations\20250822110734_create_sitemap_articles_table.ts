import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('sitemap_articles', (table) => {
    table.increments('id').primary();
    table.integer('article_id').notNullable();
    table.integer('sitemap_id').notNullable();
    table.timestamps(true, true);

    table.foreign('article_id').references('articles.id').onDelete('CASCADE');
    table.foreign('sitemap_id').references('sitemaps.id').onDelete('CASCADE');

    table.unique(['article_id', 'sitemap_id']);

    table.index(['article_id']);
    table.index(['sitemap_id']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('sitemap_articles');
}
