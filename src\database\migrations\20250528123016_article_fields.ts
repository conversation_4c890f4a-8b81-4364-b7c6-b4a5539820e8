import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('articles', (table) => {
    table.string('thumbnail_url').nullable();
    table.specificType('thumbnail_keywords', 'text[]').nullable();
    table.specificType('thumbnail_embedding', 'vector(768)').nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('articles', (table) => {
    table.dropColumn('thumbnail_url');
    table.dropColumn('thumbnail_keywords');
    table.dropColumn('thumbnail_embedding');
  });
}
