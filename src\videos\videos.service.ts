import { Injectable } from '@nestjs/common';
import { Knex } from 'knex';
import { Inject } from '@nestjs/common';
import { QueueService } from 'src/queue/queue.service';

interface Video {
  id: string;
  creator_id: string;
  keywords: string[];
}

@Injectable()
export class VideosService {
  constructor(
    @Inject('KNEX_CONNECTION') private knex: Knex,
    private queueService: QueueService,
  ) {}

  async getVideos(): Promise<Video[]> {
    return this.knex('videos').select('*');
  }

  async addEmbedding(id: string, embedding: string) {
    await this.knex('videos').where('id', id).update({ embedding });
  }

  async createVideo(video: Video) {
    await this.knex('videos').insert(video);

    return this.queueService.addEmbeddingJobVideo(
      video.id,
      video.keywords.join(','),
    );
  }
}
