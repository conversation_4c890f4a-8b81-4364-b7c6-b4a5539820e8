import { Test, TestingModule } from '@nestjs/testing';
import { AdCheckerController } from './ad-checker.controller';
import { AdCheckerService } from './ad-checker.service';

describe('AdCheckerController', () => {
  let controller: AdCheckerController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdCheckerController],
      providers: [
        {
          provide: AdCheckerService,
          useValue: {
            checkVisibility: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<AdCheckerController>(AdCheckerController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
