import { Test, TestingModule } from '@nestjs/testing';
import { AdCheckerService } from './ad-checker.service';
import { QueueService } from '../queue/queue.service';
import { WebsitesService } from '../websites/websites.service';

describe('AdCheckerService', () => {
  let service: AdCheckerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdCheckerService,
        {
          provide: 'KNEX_CONNECTION',
          useValue: {},
        },
        {
          provide: QueueService,
          useValue: {
            getAdCheckQueueCount: jest.fn(),
            addAdCheckJob: jest.fn(),
          },
        },
        {
          provide: WebsitesService,
          useValue: {
            updateWebsite: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AdCheckerService>(AdCheckerService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
