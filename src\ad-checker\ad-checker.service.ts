import { Injectable } from '@nestjs/common';
import { Knex } from 'knex';
import { Inject } from '@nestjs/common';
import { Article } from 'src/articles/articles.service';
import { QueueService } from 'src/queue/queue.service';
import { WebsitesService } from 'src/websites/websites.service';
import { Cron } from '@nestjs/schedule';
import { Website } from '../websites/models/website.interface';

@Injectable()
export class AdCheckerService {
  constructor(
    @Inject('KNEX_CONNECTION') private knex: Knex,
    private queueService: QueueService,
    private websitesService: WebsitesService,
  ) {}

  @Cron('0 0 * * *') // Runs every day at midnight
  async checkVisibility() {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7); // Calculate the date 7 days ago

    console.log('Starting AdCheck for websites...');

    // Select websites where last_ad_check is null OR older than one week
    const websites = await this.knex<Website>('websites')
      .where('ad_check_frequency', 'weekly') // Only retrieve websites with 'weekly' ad check frequency
      .andWhere((builder) => {
        builder
          .where('last_ad_check', '<', oneWeekAgo.toISOString())
          .orWhereNull('last_ad_check');
      })
      .select('*');

    console.log('Websites to check:', websites.length);

    for (const website of websites) {
      const allArticles = await this.knex<Article>('articles')
        .where('website_id', website.id)
        .select('url', 'id');
      console.log('Articles to check:', allArticles.length);

      if (allArticles.length === 0) {
        await this.websitesService.updateWebsite(website.id, {
          last_ad_check: new Date(),
        });
        continue; // Move to the next website
      }

      for (const article of allArticles) {
        await this.queueService.addAdCheckJob(article.url, article.id);
      }

      // Update the timestamp for the website when all its articles are processed.
      await this.websitesService.updateWebsite(website.id, {
        last_ad_check: new Date(),
      });
      console.log('AdCheck sheduled');
    }
  }
}
