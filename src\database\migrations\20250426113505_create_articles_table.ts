import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('articles', (table) => {
      table.increments('id').primary();
      table.string('url').notNullable();
      table.integer('website_id').notNullable();
      table.specificType('textContent', 'text');
      table.specificType('keywords', 'text[]');
      table.specificType('embedding', 'vector(768)');
      table.jsonb('ad_check_result');
      table.timestamps(true, true);
      table.foreign('website_id').references('websites.id');
    })
    .raw(
      'CREATE INDEX articles_embedding_idx ON articles USING ivfflat (embedding vector_l2_ops)',
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('articles');
}
