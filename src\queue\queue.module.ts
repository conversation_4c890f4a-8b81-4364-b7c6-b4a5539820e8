import { BullModule } from '@nestjs/bullmq';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ArticlesModule } from 'src/articles/articles.module';
import { VideosModule } from 'src/videos/videos.module';
import { QueueController } from './queue.controller';
import { QueueService } from './queue.service';
import { DatabaseModule } from 'src/database/database.module';

@Module({
  controllers: [QueueController],
  providers: [QueueService],
  imports: [
    ConfigModule,
    DatabaseModule,
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        connection: {
          url: configService.getOrThrow('REDIS_URL'),
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue(
      { name: 'scrape' },
      { name: 'embedding' },
      { name: 'ad-check' },
      { name: 'ad-check' },
    ),
    ArticlesModule,
    forwardRef(() => VideosModule),
  ],
  exports: [BullModule, QueueService],
})
export class QueueModule {}
