import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  LambdaRequest,
  LambdaResponse,
  AdCheckRequest,
} from './models/lambda-request.interface';
import { AdCheckJobResult } from 'src/ad-checker/models/ad-check-job-result.interface';
import pLimit from 'p-limit';
import { appendFile } from 'fs/promises';
import { dirname } from 'path';
import { mkdirSync } from 'fs';

/**
 * Service for interacting with AWS Lambda functions
 */
@Injectable()
export class AwsService {
  private readonly logger = new Logger(AwsService.name);
  private readonly adCheckLambdaUrl: string | undefined;
  private readonly defaultTimeout: number = 5 * 60 * 1000; // 5 minutes
  private readonly awsLogFile: string;

  constructor(private configService: ConfigService) {
    this.adCheckLambdaUrl = this.configService.get<string>(
      'AD_CHECK_LAMBDA_URL',
    );
    this.awsLogFile =
      this.configService.get<string>('AWS_LOG_FILE') || './logs/aws.log';

    // Ensure log directory exists (best-effort)
    try {
      const dir = dirname(this.awsLogFile);
      if (dir) {
        mkdirSync(dir, { recursive: true });
      }
    } catch (e) {
      // ignore errors creating log directory, we'll handle write errors later
      this.logger.warn(
        'Could not create log directory for ' + this.awsLogFile + ': ' + e,
      );
    }
  }

  /**
   * Invoke a generic Lambda function
   */
  async invokeLambda<T>(
    endpoint: string,
    payload: LambdaRequest,
    timeout: number = this.defaultTimeout,
  ): Promise<T> {
    const startTime = Date.now();

    const appendLog = async (
      level: 'info' | 'error' | 'debug',
      context: string,
      data: unknown,
    ): Promise<void> => {
      const safeStringify = (v: unknown) => {
        try {
          return JSON.stringify(v);
        } catch {
          try {
            return String(v);
          } catch {
            return '<unserializable>';
          }
        }
      };

      const entry = {
        timestamp: new Date().toISOString(),
        level,
        context,
        endpoint,
        data: safeStringify(data),
      };

      try {
        await appendFile(this.awsLogFile, JSON.stringify(entry) + '\n', 'utf8');
      } catch (err) {
        // Don't let logging failures break the main flow. Log locally.
        this.logger.warn(
          'Failed to write AWS log to ' + this.awsLogFile + ': ' + err,
        );
      }
    };

    try {
      this.logger.debug(
        `Invoking Lambda at ${endpoint} with payload:`,
        payload,
      );
      // write request log (fire-and-forget)
      void appendLog('info', 'invoke.request', { payload });

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'datalake-api/1.0',
        },
        body: JSON.stringify(payload),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        // attempt to capture response text
        let respText = '';
        try {
          respText = await response.text();
        } catch (e) {
          respText = `<failed to read response body: ${e}>`;
        }

        await appendLog('error', 'invoke.response_error', {
          status: response.status,
          statusText: response.statusText,
          body: respText,
        });

        throw new Error(
          `Lambda request failed with status ${response.status}: ${response.statusText}`,
        );
      }

      const responseBody = (await response.json()) as LambdaResponse<T>;
      const executionTime = Date.now() - startTime;

      if (!responseBody.success) {
        await appendLog('error', 'invoke.response_failure', { responseBody });
        throw new Error(`Lambda request failed: ${responseBody.error}`);
      }

      this.logger.debug(
        `Lambda invocation completed in ${executionTime}ms with success: ${responseBody.success}`,
      );

      // write success response (exclude large fields if needed)
      void appendLog('info', 'invoke.response', {
        executionTime,
        responseBody,
      });

      return responseBody.result;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.logger.error(
        `Lambda invocation failed after ${executionTime}ms:`,
        error,
      );

      // log error to file
      try {
        const errEntry = {
          timestamp: new Date().toISOString(),
          level: 'error',
          context: 'invoke.exception',
          endpoint,
          error: {
            name: (error as Error).name,
            message: (error as Error).message,
            stack: (error as Error).stack,
          },
        };

        await appendFile(
          this.awsLogFile,
          JSON.stringify(errEntry) + '\n',
          'utf8',
        );
      } catch (e) {
        this.logger.warn(
          'Failed to write AWS error log to ' + this.awsLogFile + ': ' + e,
        );
      }

      if ((error as Error).name === 'AbortError') {
        throw new HttpException(
          'Lambda request timeout',
          HttpStatus.REQUEST_TIMEOUT,
        );
      }

      throw new HttpException(
        `Lambda invocation failed: ${(error as Error).message}`,
        HttpStatus.BAD_GATEWAY,
      );
    }
  }

  /**
   * Invoke the ad check Lambda function
   */
  async invokeAdCheckLambda(
    url: string,
    articleId: number,
    layers: ['placement', 'visibility'] = ['placement', 'visibility'],
  ): Promise<AdCheckJobResult> {
    if (!this.adCheckLambdaUrl) {
      throw new HttpException(
        'AD_CHECK_LAMBDA_URL is not configured',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const payload: AdCheckRequest = {
      url,
      articleId,
      layers,
      metadata: {
        timestamp: new Date().toISOString(),
        source: 'datalake-api',
      },
    };

    const result = await this.invokeLambda(this.adCheckLambdaUrl, payload);

    return result as AdCheckJobResult;
  }

  /**
   * Batch invoke ad check Lambda for multiple URLs
   */
  async batchInvokeAdCheckLambda(
    articles: { id: number; url: string }[],
    layers: ['placement', 'visibility'] = ['placement', 'visibility'],
  ): Promise<AdCheckJobResult[]> {
    if (!this.adCheckLambdaUrl) {
      throw new HttpException(
        'AD_CHECK_LAMBDA_URL is not configured',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    this.logger.log(`Starting batch ad check for ${articles.length} URLs`);

    // Limit concurrent requests to 30 to avoid Lambda rate limiting
    const limit = pLimit(100);

    const results = await Promise.allSettled(
      articles.map((article) =>
        limit(() => this.invokeAdCheckLambda(article.url, article.id, layers)),
      ),
    );

    return results
      .map((result) => (result.status === 'fulfilled' ? result.value : null))
      .filter((a) => !!a);
  }
}
