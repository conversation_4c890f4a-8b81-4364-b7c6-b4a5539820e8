import { Inject, Injectable } from '@nestjs/common';
import { Knex } from 'knex';
import { Sitemap } from './models/sitemap.interface';

@Injectable()
export class SitemapsService {
  constructor(@Inject('KNEX_CONNECTION') private knex: Knex) {}

  async getSitemapsByWebsiteId(websiteId: number) {
    return this.knex<Sitemap>('sitemaps')
      .where('website_id', websiteId)
      .select('*');
  }

  async createSitemap(url: string, websiteId: number) {
    const [sitemap] = await this.knex<Sitemap>('sitemaps')
      .insert({
        url,
        website_id: websiteId,
      })
      .returning('*');
    return sitemap;
  }

  async deleteSitemap(id: number) {
    return this.knex<Sitemap>('sitemaps').where('id', id).del();
  }
}
