import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.raw(
    'CREATE INDEX articles_openai_embeddings_small_idx ON articles USING ivfflat (openai_embeddings_small vector_l2_ops)',
  );
  await knex.schema.raw(
    'CREATE INDEX videos_openai_embeddings_small_idx ON videos USING ivfflat (openai_embeddings_small vector_l2_ops)',
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.raw('DROP INDEX articles_openai_embeddings_small_idx');
  await knex.schema.raw('DROP INDEX videos_openai_embeddings_small_idx');
}
