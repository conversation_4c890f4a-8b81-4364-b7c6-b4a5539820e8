import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('videos', (table) => {
    table.dropColumn('embedding');
  });

  await knex.schema.alterTable('videos', (table) => {
    table.specificType('embedding', 'vector(1536)');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('videos', (table) => {
    table.dropColumn('embedding');
  });

  await knex.schema.alterTable('videos', (table) => {
    table.specificType('embedding', 'vector(768)');
  });
}
