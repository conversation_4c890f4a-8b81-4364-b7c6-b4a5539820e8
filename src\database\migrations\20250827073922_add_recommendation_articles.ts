import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('recommendation_articles', function (table) {
    table.increments('id').primary();
    table.text('title');
    table.text('url').unique();
    table.text('image_url');
    table.text('description');
    table.jsonb('tfidf_vector').nullable().defaultTo(null);
    table.timestamps(true, true);

    table.index('tfidf_vector', 'idx_recommendation_articles_tfidf', 'GIN');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.raw('DROP INDEX IF EXISTS idx_recommendation_articles_tfidf');

  await knex.schema.dropTable('recommendation_articles');
}
