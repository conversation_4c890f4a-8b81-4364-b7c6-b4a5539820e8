import { Body, Controller, Post } from '@nestjs/common';
import { VideosService } from './videos.service';

interface Video {
  id: string;
  creator_id: string;
  keywords: string[];
}

@Controller('videos')
export class VideosController {
  constructor(private readonly videosService: VideosService) {}

  @Post('create')
  async create(@Body() video: Video) {
    return this.videosService.createVideo(video);
  }
}
