import { AdCheckLayer } from './ad-check-layer.type';
import { AdCheckIssues } from './ad-check-issues.type';
import { AdCheckDeviceError } from './ad-check-device-error.type';

export interface AdCheckJobResultBase {
  id: number;
  url: string;
  timestamp: string;
}

export interface AdCheckJobSuccessResult extends AdCheckJobResultBase {
  success: true;
  checkedLayers: AdCheckLayer[];
  processingTimeMs: number;
}

export interface AdCheckJobFailureResult extends AdCheckJobResultBase {
  success: false;
  totalIssues: number;
  checkedLayers: AdCheckLayer[];
  issues: AdCheckIssues;
  errors: AdCheckDeviceError[];
  processingTimeMs: number;
}

export interface AdCheckJobErrorResult extends AdCheckJobResultBase {
  error: string;
}

export type AdCheckJobResult =
  | AdCheckJobSuccessResult
  | AdCheckJobFailureResult
  | AdCheckJobErrorResult;
