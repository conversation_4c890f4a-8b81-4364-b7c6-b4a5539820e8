import { InjectQueue } from '@nestjs/bullmq';
import { Queue, QueueEvents } from 'bullmq';
import {
  forwardRef,
  Inject,
  Injectable,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import { ArticlesService } from 'src/articles/articles.service';
import { VideosService } from 'src/videos/videos.service';
import { ConfigService } from '@nestjs/config';
import { AdCheckJobResult } from 'src/ad-checker/models/ad-check-job-result.interface';

interface ScrapeJobResult {
  url: string;
  text: string;
}

interface EmbeddingJobResult {
  id?: string;
  url?: string;
  text: string;
  embedding: number[];
  type: 'article' | 'video';
}

@Injectable()
export class QueueService implements OnModuleInit, OnModuleDestroy {
  constructor(
    @InjectQueue('scrape') private scrapeQueue: Queue,
    @InjectQueue('embedding') private embeddingQueue: Queue,
    @InjectQueue('ad-check') private adCheckQueue: Queue,

    private articleService: ArticlesService,
    @Inject(forwardRef(() => VideosService))
    private videosService: VideosService,
    private configService: ConfigService,
  ) {}

  private queueEventsScrape: QueueEvents;
  private queueEventsEmbedding: QueueEvents;
  private queueEventsAdCheck: QueueEvents;

  async onModuleInit() {
    const redisConnection = this.configService.get<string>('REDIS_URL');

    // ### Scrape Queue
    this.queueEventsScrape = new QueueEvents('scrape', {
      connection: {
        url: redisConnection,
      },
    });

    await this.queueEventsScrape.waitUntilReady();

    this.queueEventsScrape.on(
      'completed',
      ({ jobId, returnvalue }: { jobId: string; returnvalue: any }) => {
        void (async () => {
          console.log(`Job ${jobId} completed with result:`, returnvalue);

          const result = returnvalue as ScrapeJobResult;

          const keywords = await this.articleService.updateArticle(
            result.url,
            result.text,
          );

          if (!keywords) {
            console.error(`No keywords found for article ${result.url}`);
            return;
          }

          this.addEmbeddingJobArticle(result.url, keywords.join(', ')).catch(
            (err) => {
              console.error(`Error adding embedding job:`, err);
            },
          );
        })();
      },
    );

    this.queueEventsScrape.on('failed', ({ jobId, failedReason }) => {
      console.error(`Job ${jobId} failed with reason:`, failedReason);
    });

    // ### Embedding Queue
    this.queueEventsEmbedding = new QueueEvents('embedding', {
      connection: {
        url: redisConnection,
      },
    });

    await this.queueEventsEmbedding.waitUntilReady();

    this.queueEventsEmbedding.on(
      'completed',
      ({ jobId, returnvalue }: { jobId: string; returnvalue: any }) => {
        console.log(`Job ${jobId} completed with result:`, returnvalue);

        const result = returnvalue as EmbeddingJobResult;

        if (result.type === 'article') {
          if (!result.url) {
            console.error(`Article URL is undefined for job ${jobId}`);
            return;
          }

          const formattedVector = `[${result.embedding.join(',')}]`;

          this.articleService
            .addEmbedding(result.url, formattedVector)
            .then(() => {
              console.log(`Article ${result.url} updated with embedding`);
              this.embeddingQueue.remove(jobId).catch((err) => {
                console.error(`Error removing job:`, err);
              });
            })
            .catch((err) => {
              console.error(`Error updating article:`, err);
            });
        }

        if (result.type === 'video') {
          if (!result.id) {
            console.error(`Video ID is undefined for job ${jobId}`);
            return;
          }

          const formattedVector = `[${result.embedding.join(',')}]`;

          this.videosService
            .addEmbedding(result.id, formattedVector)
            .then(() => {
              console.log(`Video ${result.id} updated with embedding`);
              this.embeddingQueue.remove(jobId).catch((err) => {
                console.error(`Error removing job:`, err);
              });
            })
            .catch((err) => {
              console.error(`Error updating video:`, err);
            });
        }
      },
    );

    this.queueEventsEmbedding.on('failed', ({ jobId, failedReason }) => {
      console.error(`Job ${jobId} failed with reason:`, failedReason);
    });

    this.queueEventsAdCheck = new QueueEvents('ad-check', {
      connection: {
        url: redisConnection,
      },
    });

    await this.queueEventsAdCheck.waitUntilReady();
    this.initiateListenersForAdCheckQueue();
  }

  async onModuleDestroy() {
    await this.queueEventsScrape.close();
    await this.queueEventsEmbedding.close();
    await this.queueEventsAdCheck.close();
    await this.queueEventsAdCheck.close();
  }

  async addScrapeJob(url: string) {
    await this.scrapeQueue.add('scrape', { url });
  }

  async addEmbeddingJobArticle(url: string, text: string) {
    await this.embeddingQueue.add('embedding', { url, text, type: 'article' });
  }

  async addEmbeddingJobVideo(id: string, text: string) {
    await this.embeddingQueue.add('embedding', { id, text, type: 'video' });
  }

  async addAdCheckJob(url: string, articleId: number) {
    const layers = ['placement', 'visibility'];
    await this.adCheckQueue.add('ad-check', {
      url,
      articleId,
      layers,
    });
  }

  async getAdCheckerJobsCount() {
    return this.adCheckQueue.getJobCounts();
  }

  async triggerVideoEmbeddings() {
    const videos = await this.videosService.getVideos();
    for (const video of videos) {
      await this.addEmbeddingJobVideo(video.id, video.keywords.join(', '));
    }
  }

  async embedArticles() {
    const articles = await this.articleService.getArticles();
    for (const article of articles) {
      console.log(article.url);
      if (!article.keywords || article.keywords.length === 0) {
        console.log('No keywords found for article', article.url);
        continue;
      }

      await this.addEmbeddingJobArticle(
        article.url,
        article.keywords.join(', '),
      );
    }
  }

  private initiateListenersForAdCheckQueue() {
    this.queueEventsAdCheck.on(
      'completed',
      ({ jobId, returnvalue }: { jobId: string; returnvalue: unknown }) => {
        void (async () => {
          const result = returnvalue as AdCheckJobResult;
          const { id } = result;

          if (!id) {
            console.error(`URL is missing from ad-check job ${jobId} result.`);
            return;
          }

          try {
            await this.articleService.updateArticleById(id, {
              ad_check_result: result,
            });
            console.log(`Stored ad-check result for article: ${id}`);
          } catch (error) {
            console.error(`Failed to store ad-check result for ${id}:`, error);
          }

          this.adCheckQueue.remove(jobId).catch((err) => {
            console.error(`Error removing ad-check job:`, err);
          });
        })();
      },
    );

    this.queueEventsAdCheck.on('failed', ({ jobId, failedReason }) => {
      console.error(`ad-check Job ${jobId} failed with reason:`, failedReason);
    });
  }
}
