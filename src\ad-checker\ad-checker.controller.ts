import { <PERSON>, Post } from '@nestjs/common';
import { AdCheckerService } from './ad-checker.service';

@Controller('ad-checker')
export class AdCheckerController {
  constructor(private readonly AdCheckerService: AdCheckerService) {}

  @Post('trigger')
  triggerCheckVisibility() {
    this.AdCheckerService.checkVisibility();
    return { message: 'Visibility check triggered successfully.' };
  }
}
