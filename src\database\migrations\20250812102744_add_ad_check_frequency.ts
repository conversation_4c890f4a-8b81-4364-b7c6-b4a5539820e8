import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('websites', (table) => {
    table
      .enum('ad_check_frequency', ['never', 'weekly'])
      .notNullable()
      .defaultTo('never');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('websites', (table) => {
    table.dropColumn('ad_check_frequency');
  });
}
